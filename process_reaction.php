<?php
/**
 * External Trigger Endpoint for Delayed Reactions
 * Called by external cron services to process one delayed reaction
 */

// Security configuration
$SECRET_KEY = 'your-secret-key-here-change-this'; // Change this to a secure random string
$BASE_URL = 'https://yourserver.com'; // Change to your actual domain

// Include required files
require_once __DIR__ . '/shared_hosting_queue.php';

// Bot tokens (same as in bot.php)
$botTokens = [
    '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '4'  => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '5'  => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '6'  => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '7'  => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '8'  => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '9'  => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '10' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '11' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '12' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '13' => '7339986151:AAETfpuU3ksUtk5GSkLkPsrb9tGiccSb9dg',
    '14' => '7748840812:AAHDjQVZL1NAkHUMUYGuBqu0sAudlXEc2Uo',
    '15' => '7728027518:AAEBUOi_BPYqqu8qXOvYQfM9GHfVUDwJ0-M',
    '16' => '7601378202:AAHi2Jo0t_W8sEWnxWkDlZqjYAwpUGimG6k',
    '17' => '8005056905:AAGBc7HjP4gvAlrnKrelz94A4mNSgDBqLmg',
    '18' => '5920970906:AAEl59pHfUw0WnqkPnR5VH4HWq35mL_m0u0',
    '19' => '7957888180:AAEsxFpBqlwI1snuXdLxBM9feYo8GrxL71U',
    '20' => '1874727878:AAGhz33r6JB7jf5LA5GmXKuu-NkKE-juO4Y',
    '21' => '1782934509:AAHAvy6rIDj9WOi3N6Waz_MbSbzghrDBsb0',
    '22' => '1854747800:AAFrh45kpaQ7UOMx7rF4YOADrFCh2P3x5Fs',
    '23' => '1812296269:AAE8KpwmmO73Bg9uIxgiihvhz8wd6P-0-ww',
    '24' => '1833448524:AAE1C4jzssdPGA5iYlmrNtWchE6I0E6gURw',
    '25' => '2054730466:AAGaTrFU4UvA2qrt2vmUTBB72G1UFzg7MXk',
    '26' => '2103424776:AAGkaXs8NqiFjfoXJ7G2RPzNMnhpLXCJG98',
    '27' => '5232551378:AAEA6kDOIAUPhl44Eqzo_jTJNV09cHa3Ifg',
    '28' => '5076605250:AAFIwFnhMd16iW27l5SOszE1KVU_X3dLbA4',
    '29' => '5116250996:AAG-a_xoeczvkCr_Z_Ggus9Fu_C49OqgmZ4',
    '30' => '7710186495:AAF46DoNSUQ9PEEDwREx8aOK42Du1rhyXoE',
    '31' => '5989552141:AAGEQ1ObcCmaO68EvwU3XFMRuRHODSAv2o0',
    '32' => '5954755638:AAHyHoMUMmkTgFON6Cwzkq4Pu0bXMVrZKo4',
    '33' => '7638711403:AAFlCDzFZohgrW-duuPiijRMRT-BtKO9a2s',
    '34' => '7992531992:AAHgqBoygZ-vEvGiihwGuaMwG5d3v3BQ-TI',
    '35' => '7680297842:AAGPmB2yufn-n6-xNLIBag5eYEchh6pIF48',
    '36' => '7529351450:AAGFFKB4VadwrZTZjjyqBhuS-bCDMi2fB0s',
    '37' => '7611442057:AAGnhvfJg_EnOITI5mrEhN8p5ehGgiM1ax0',
    '38' => '7496411088:AAF5_Hz4nysgtimtZ0xo1-QcLqFn4dAyEh0',
    '39' => '8171836495:AAFUHd4K7OcuCfnXVqpEoQFBLd4bhxf4maA',
    '40' => '7309785917:AAEDGLAsBw5yEraoPhSO_PfYCkvSyNwGHts',
    '41' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
    '42' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
    '43' => '7611709153:AAGEjbgr-KZcyDMn7Z5dfQwZZPgLSHssp6Y',
    '44' => '8000444345:AAHicXHhUnRptpI3yp26MB0Af_9rWC9t3QE',
];

// Log file
define('LOGFILE', __DIR__ . '/delayed_reactions.log');

/**
 * Log message with timestamp
 */
function logMessage($message) {
    $timestamp = date('[Y-m-d H:i:s]');
    $logEntry = "{$timestamp} {$message}\n";
    file_put_contents(LOGFILE, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Send reaction via Telegram API
 */
function sendReaction($botId, $chatId, $messageId, $emoji) {
    global $botTokens;
    
    if (!isset($botTokens[(string)$botId])) {
        return false;
    }
    
    $botToken = $botTokens[(string)$botId];
    $apiURL = "https://api.telegram.org/bot{$botToken}/";
    
    $url = $apiURL . "setMessageReaction"
         . "?chat_id={$chatId}"
         . "&message_id={$messageId}"
         . "&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$emoji}\"}]"
         . "&is_big=true";
    
    // Use cURL for better error handling
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Multibot-DelayedReaction/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        logMessage("cURL error for bot {$botId}: {$error}");
        return false;
    }
    
    if ($httpCode !== 200) {
        logMessage("HTTP error {$httpCode} for bot {$botId}: {$response}");
        return false;
    }
    
    $result = json_decode($response, true);
    return isset($result['ok']) && $result['ok'] === true;
}

/**
 * Schedule next reaction using external cron service
 */
function scheduleNextReaction($queue) {
    global $BASE_URL, $SECRET_KEY;
    
    $cronInfo = $queue->generateCronUrl($BASE_URL, $SECRET_KEY);
    
    if ($cronInfo === null) {
        logMessage("No more reactions to schedule");
        return;
    }
    
    // Log the scheduling information
    logMessage("Next reaction scheduled for: " . $cronInfo['schedule_datetime']);
    logMessage("Cron URL: " . $cronInfo['url']);
    
    // Here you would typically call an external cron service API
    // For now, we just log the information for manual setup
    
    // Example for cron-job.org API (requires account and API key):
    /*
    $cronJobData = [
        'job' => [
            'url' => $cronInfo['url'],
            'enabled' => true,
            'schedule' => [
                'timezone' => 'UTC',
                'expiresAt' => $cronInfo['schedule_time'] + 60, // Expire 1 minute after
                'hours' => [date('H', $cronInfo['schedule_time'])],
                'mdays' => [date('j', $cronInfo['schedule_time'])],
                'minutes' => [date('i', $cronInfo['schedule_time'])],
                'months' => [date('n', $cronInfo['schedule_time'])],
                'wdays' => [-1]
            ]
        ]
    ];
    
    // Call cron-job.org API to schedule
    // Implementation depends on the external service you choose
    */
}

// Main processing logic
try {
    // Security check
    $providedKey = $_GET['key'] ?? '';
    if ($providedKey !== $SECRET_KEY) {
        http_response_code(403);
        logMessage("Unauthorized access attempt with key: " . $providedKey);
        exit('Unauthorized');
    }
    
    // Initialize queue manager
    $queue = new SharedHostingQueue();
    
    // Get next reaction to process
    $nextReaction = $queue->getNextReaction();
    
    if ($nextReaction === null) {
        // No reactions ready, clean up old posts
        $cleaned = $queue->cleanupOldPosts();
        if ($cleaned > 0) {
            logMessage("Cleaned up {$cleaned} old posts");
        }
        
        http_response_code(200);
        echo json_encode(['status' => 'no_reactions_ready', 'cleaned' => $cleaned]);
        exit;
    }
    
    $postId = $nextReaction['post_id'];
    $botId = $nextReaction['current_bot'];
    $chatId = $nextReaction['chat_id'];
    $messageId = $nextReaction['message_id'];
    $emojiPool = $nextReaction['emoji_pool'];
    
    // Skip if bot ID is invalid
    if ($botId > 44 || !isset($botTokens[(string)$botId])) {
        logMessage("Invalid bot ID {$botId} for post {$postId}");
        $queue->markReactionSent($postId, $botId, '', false);
        
        http_response_code(400);
        echo json_encode(['status' => 'invalid_bot_id', 'bot_id' => $botId]);
        exit;
    }
    
    // Select random emoji
    $emoji = $emojiPool[array_rand($emojiPool)];
    
    // Send reaction
    $success = sendReaction($botId, $chatId, $messageId, $emoji);
    
    // Update progress
    $queue->markReactionSent($postId, $botId, $emoji, $success);
    
    $status = $success ? 'SUCCESS' : 'FAILED';
    logMessage("Bot #{$botId} → '{$emoji}' for post {$postId} [{$status}]");
    
    // Schedule next reaction if this one succeeded
    if ($success && $botId < 44) {
        scheduleNextReaction($queue);
    }
    
    // Return response
    http_response_code(200);
    echo json_encode([
        'status' => $success ? 'success' : 'failed',
        'post_id' => $postId,
        'bot_id' => $botId,
        'emoji' => $emoji,
        'next_bot' => $botId + 1,
        'completed' => $botId >= 44
    ]);
    
} catch (Exception $e) {
    logMessage("Error in process_reaction.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}

// Handle manual trigger for testing
if (isset($_GET['test']) && $_GET['test'] === '1') {
    echo "<h2>Delayed Reaction Processor - Test Mode</h2>";
    
    $queue = new SharedHostingQueue();
    $status = $queue->getQueueStatus();
    
    echo "<h3>Queue Status:</h3>";
    echo "<ul>";
    echo "<li>Total posts: " . $status['total_posts'] . "</li>";
    echo "<li>Active posts: " . $status['active_posts'] . "</li>";
    echo "<li>Completed posts: " . $status['completed_posts'] . "</li>";
    echo "<li>Pending reactions: " . $status['pending_reactions'] . "</li>";
    echo "</ul>";
    
    $detailed = $queue->getDetailedQueue();
    if (!empty($detailed)) {
        echo "<h3>Detailed Queue:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Post ID</th><th>Status</th><th>Current Bot</th><th>Next Reaction</th></tr>";
        foreach ($detailed as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['post_id']) . "</td>";
            echo "<td>" . htmlspecialchars($item['status']) . "</td>";
            echo "<td>" . $item['current_bot'] . "/44</td>";
            echo "<td>" . $item['next_reaction_time'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $cronInfo = $queue->generateCronUrl($BASE_URL, $SECRET_KEY);
    if ($cronInfo) {
        echo "<h3>Next Scheduled Reaction:</h3>";
        echo "<p>Time: " . $cronInfo['schedule_datetime'] . "</p>";
        echo "<p>URL: <code>" . htmlspecialchars($cronInfo['url']) . "</code></p>";
    }
}
